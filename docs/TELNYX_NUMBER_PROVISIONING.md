# Telnyx Number Provisioning

This document describes the Telnyx DID (Direct Inward Dialing) provisioning system for the AI Voice Receptionist platform.

## Overview

The Telnyx number provisioning system allows tenants to:
- Search for available phone numbers by US state
- Purchase phone numbers for their account
- Manage phone number status and configuration
- Receive real-time updates via webhooks

## Architecture

### Components

1. **TelnyxService** - Core service for Telnyx API interactions
2. **API Endpoints** - RESTful endpoints for number management
3. **Webhook Handlers** - Process Telnyx status updates
4. **Database Models** - Store number ownership and metadata

### Database Schema

The `telnyx_numbers` table stores provisioned numbers:

```sql
CREATE TABLE telnyx_numbers (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL,
    did VARCHAR(20) NOT NULL UNIQUE,
    state VARCHAR(2) NOT NULL,
    forwarding_number VARCHAR(20),
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    telnyx_number_id VARCHAR(100),
    metadata JSONB,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);
```

### Status Flow

1. **pending** - Number purchased, awaiting Telnyx activation
2. **active** - Number active and ready for use
3. **inactive** - Number suspended or deactivated
4. **failed** - Provisioning failed

## API Endpoints

### Search Available Numbers

Search for available phone numbers in a specific US state and optionally city.

```bash
GET /api/v1/numbers/available?state=TX&city=Houston&limit=10
X-Tenant-ID: {tenant-uuid}
```

**Parameters:**
- `state` (required) - Two-letter US state code (e.g., TX, CA, NY)
- `city` (optional) - City name (e.g., Houston, Austin, Dallas)
- `limit` (optional) - Number of results to return (1-250, default: 10)

**Response:**
```json
{
  "numbers": [
    {
      "phone_number": "+***********",
      "region_information": [
        {
          "region_type": "state",
          "region_name": "Texas"
        }
      ],
      "features": ["voice", "sms"],
      "cost_information": {
        "upfront_cost": "1.00",
        "monthly_cost": "1.00",
        "currency": "USD"
      },
      "quickship": true,
      "reservable": true
    }
  ],
  "total_count": 1,
  "state": "TX",
  "city": "Houston"
}
```

### Purchase Number

Purchase a phone number for the tenant's account.

```bash
POST /api/v1/numbers/purchase
X-Tenant-ID: {tenant-uuid}
Content-Type: application/json

{
  "state": "TX",
  "did": "+***********"
}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "tenant_id": "550e8400-e29b-41d4-a716-************",
  "did": "+***********",
  "state": "TX",
  "forwarding_number": null,
  "status": "pending",
  "telnyx_number_id": "telnyx_123",
  "metadata": {
    "purchase_response": {...},
    "purchased_at": "2024-12-19T10:00:00Z"
  },
  "created_at": "2024-12-19T10:00:00Z",
  "updated_at": "2024-12-19T10:00:00Z"
}
```

### List Tenant Numbers

Get all phone numbers owned by the tenant.

```bash
GET /api/v1/numbers/
X-Tenant-ID: {tenant-uuid}

# Optional: filter by status
GET /api/v1/numbers/?status=active
```

**Response:**
```json
[
  {
    "id": "550e8400-e29b-41d4-a716-************",
    "tenant_id": "550e8400-e29b-41d4-a716-************",
    "did": "+***********",
    "state": "TX",
    "status": "active",
    "created_at": "2024-12-19T10:00:00Z",
    "updated_at": "2024-12-19T10:00:00Z"
  }
]
```

### Get Number Details

Get details for a specific phone number.

```bash
GET /api/v1/numbers/{number-id}
X-Tenant-ID: {tenant-uuid}
```

## Webhook Configuration

### Setup

1. **Configure webhook URL** in your Telnyx portal:
   ```
   https://your-domain.com/webhooks/telnyx/number-activated
   ```

2. **Set webhook secret** in environment variables:
   ```bash
   TELNYX_WEBHOOK_SECRET=your_webhook_secret_here
   ```

3. **Subscribe to events** in Telnyx portal:
   - `phone_number.created`
   - `phone_number.updated`
   - `phone_number.deleted`

### Webhook Payload

Telnyx sends webhooks with this structure:

```json
{
  "data": {
    "id": "telnyx_number_123",
    "phone_number": "+***********",
    "status": "active",
    "connection_id": null,
    "customer_reference": "tenant-uuid",
    "created_at": "2024-12-19T10:00:00Z",
    "updated_at": "2024-12-19T10:00:00Z"
  },
  "event_type": "phone_number.created",
  "id": "webhook_event_123",
  "occurred_at": "2024-12-19T10:00:00Z",
  "record_type": "event"
}
```

### Signature Verification

Webhooks are verified using HMAC-SHA256:

```python
import hmac
import hashlib

def verify_signature(payload: bytes, signature: str, secret: str) -> bool:
    # Extract v1 signature from header
    sig_parts = dict(part.split('=', 1) for part in signature.split(','))
    expected_signature = sig_parts.get('v1')
    
    # Compute signature
    computed_signature = hmac.new(
        secret.encode('utf-8'),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(expected_signature, computed_signature)
```

## Environment Variables

Required environment variables:

```bash
# Telnyx API Configuration
TELNYX_API_KEY=your_telnyx_api_key_here
TELNYX_WEBHOOK_SECRET=your_webhook_secret_here

# Database Configuration
DATABASE_URL_ASYNC=postgresql+asyncpg://user:pass@host:5432/db

# Subscription Service (for feature gating)
CORE_SUBS_API_URL=https://core-api.example.com
REDIS_URL=redis://localhost:6379/0
```

## Error Handling

### Common Error Codes

- **400** - Invalid request parameters
- **403** - Missing `voice_intake` feature in subscription
- **404** - Number not found
- **409** - Number already purchased or unavailable
- **422** - Validation error (invalid DID format, etc.)
- **500** - Internal server error
- **503** - Telnyx API unavailable

### Retry Logic

The service implements automatic retry with exponential backoff:
- **Retries**: 2 attempts for 5xx errors
- **Backoff**: 1s, 2s, 4s intervals
- **Timeout**: 30 seconds per request

## Security

### Authentication

- All endpoints require `X-Tenant-ID` header
- Subscription middleware validates `voice_intake` feature
- Tenant isolation enforced at database level

### Webhook Security

- HMAC-SHA256 signature verification
- Payload validation and sanitization
- Rate limiting recommended for production

## Testing

### Unit Tests

```bash
# Run all Telnyx tests
pytest apps/voice-svc/tests/telnyx/ -v

# Run with coverage
pytest apps/voice-svc/tests/telnyx/ --cov=services.telnyx_service --cov=routers.telnyx_numbers
```

### Integration Tests

```bash
# Test complete purchase flow
pytest apps/voice-svc/tests/telnyx/test_telnyx_integration.py::TestTelnyxIntegration::test_complete_purchase_and_activation_flow -v
```

### Manual Testing

Use the provided cURL examples to test endpoints manually:

```bash
# Search numbers by state only
curl -X GET "http://localhost:8000/api/v1/numbers/available?state=TX&limit=5" \
  -H "X-Tenant-ID: 550e8400-e29b-41d4-a716-************"

# Search numbers by state and city
curl -X GET "http://localhost:8000/api/v1/numbers/available?state=TX&city=Houston&limit=5" \
  -H "X-Tenant-ID: 550e8400-e29b-41d4-a716-************"

# Purchase number
curl -X POST "http://localhost:8000/api/v1/numbers/purchase" \
  -H "X-Tenant-ID: 550e8400-e29b-41d4-a716-************" \
  -H "Content-Type: application/json" \
  -d '{"state": "TX", "did": "+***********"}'
```

## Monitoring

### Metrics

Monitor these key metrics:
- Number search requests per minute
- Purchase success/failure rates
- Webhook processing latency
- Database query performance

### Logging

Key log events:
- Number searches and results
- Purchase attempts and outcomes
- Webhook events and processing
- API errors and retries

## Troubleshooting

### Common Issues

1. **"Invalid tenant ID format"**
   - Ensure X-Tenant-ID header contains valid UUID

2. **"Access denied: feature 'voice_intake' not available"**
   - Tenant subscription doesn't include voice_intake feature
   - Check Core API subscription service

3. **"Phone number already exists"**
   - Number already purchased by this or another tenant
   - Search for different numbers

4. **Webhook signature verification fails**
   - Check TELNYX_WEBHOOK_SECRET environment variable
   - Verify webhook URL configuration in Telnyx portal

### Debug Mode

Enable debug logging:

```bash
export DATABASE_ECHO=true
export LOG_LEVEL=DEBUG
```

## Production Deployment

### Database Migration

Run Alembic migration to create tables:

```bash
alembic upgrade head
```

### Scaling Considerations

- Use connection pooling for database
- Implement Redis caching for search results
- Consider rate limiting for API endpoints
- Monitor webhook processing queue depth
