"""
FastAPI router for Telnyx DID management endpoints.

Provides endpoints for:
- Searching available phone numbers by state
- Purchasing phone numbers
- Managing phone number status
"""
import logging
from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Header, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from packages.shared.models import TelnyxNumber, TelnyxNumberStatus
from db.session import get_db
from services.telnyx_service import TelnyxService, TelnyxAPIError
from schemas.telnyx import (
    TelnyxNumberCreate,
    TelnyxNumberResponse,
    NumberSearchResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/numbers", tags=["telnyx-numbers"])


async def get_tenant_id(x_tenant_id: str = Header(...,
                        alias="X-Tenant-ID")) -> UUID:
    """Extract tenant ID from header."""
    try:
        return UUID(x_tenant_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid tenant ID format")


@router.get(
    "/available",
    response_model=NumberSearchResponse,
    summary="Search available phone numbers",
    description="Search for available phone numbers in a specific US state"
)
async def search_available_numbers(
    state: str = Query(..., min_length=2, max_length=2, description="Two-letter US state code"),
    limit: int = Query(10, ge=1, le=250, description="Number of results to return"),
    tenant_id: UUID = Depends(get_tenant_id)
):
    """
    Search for available phone numbers by state.

    Requires the `voice_intake` feature in tenant subscription.
    """
    try:
        telnyx_service = TelnyxService()
        numbers = await telnyx_service.search_numbers(state.upper(), limit)

        return NumberSearchResponse(
            numbers=numbers,
            total_count=len(numbers),
            state=state.upper()
        )

    except TelnyxAPIError as e:
        logger.error(f"Telnyx API error for tenant {tenant_id}: {e.message}")
        raise HTTPException(
            status_code=e.status_code or 500,
            detail=e.message
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception(
            f"Unexpected error searching numbers for tenant {tenant_id}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/purchase",
    response_model=TelnyxNumberResponse,
    status_code=201,
    summary="Purchase a phone number",
    description="Purchase a phone number and add it to the tenant's account"
)
async def purchase_number(
    number_data: TelnyxNumberCreate,
    tenant_id: UUID = Depends(get_tenant_id),
    db: AsyncSession = Depends(get_db)
):
    """
    Purchase a phone number from Telnyx.

    The number will be provisioned with status 'pending' until the webhook
    confirms activation.

    Requires the `voice_intake` feature in tenant subscription.
    """
    try:
        # Check if number already exists for this tenant
        existing_query = select(TelnyxNumber).where(
            TelnyxNumber.did == number_data.did,
            TelnyxNumber.tenant_id == tenant_id
        )
        result = await db.execute(existing_query)
        existing_number = result.scalar_one_or_none()

        if existing_number:
            raise HTTPException(
                status_code=409,
                detail=f"Number {number_data.did} already exists for this tenant")

        # Check if number exists for any tenant (global uniqueness)
        global_query = select(TelnyxNumber).where(
            TelnyxNumber.did == number_data.did)
        result = await db.execute(global_query)
        global_existing = result.scalar_one_or_none()

        if global_existing:
            raise HTTPException(
                status_code=409,
                detail=f"Number {number_data.did} is already purchased"
            )

        # Purchase number from Telnyx
        telnyx_service = TelnyxService()
        telnyx_number = await telnyx_service.purchase_number(tenant_id, number_data.did)

        # Save to database
        db.add(telnyx_number)
        await db.commit()
        await db.refresh(telnyx_number)

        logger.info(
            f"Successfully purchased number {number_data.did} for tenant {tenant_id}")

        return TelnyxNumberResponse.model_validate(telnyx_number)

    except TelnyxAPIError as e:
        await db.rollback()
        logger.error(
            f"Telnyx API error purchasing {number_data.did} for tenant {tenant_id}: {e.message}")

        # Map specific error codes to HTTP status codes
        if e.error_code == "number_unavailable":
            status_code = 409
        else:
            status_code = e.status_code or 500

        raise HTTPException(status_code=status_code, detail=e.message)

    except HTTPException:
        await db.rollback()
        raise

    except ValueError as e:
        await db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

    except Exception as e:
        await db.rollback()
        logger.exception(
            f"Unexpected error purchasing number {number_data.did} for tenant {tenant_id}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/",
    response_model=List[TelnyxNumberResponse],
    summary="List tenant's phone numbers",
    description="Get all phone numbers owned by the tenant"
)
async def list_tenant_numbers(
    tenant_id: UUID = Depends(get_tenant_id),
    db: AsyncSession = Depends(get_db),
    status: str = Query(None, description="Filter by status")
):
    """
    List all phone numbers for the tenant.

    Optionally filter by status (pending, active, inactive, failed).
    """
    try:
        query = select(TelnyxNumber).where(TelnyxNumber.tenant_id == tenant_id)

        if status:
            try:
                status_enum = TelnyxNumberStatus(status.lower())
                query = query.where(TelnyxNumber.status == status_enum)
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid status. Must be one of: {[s.value for s in TelnyxNumberStatus]}"
                )

        query = query.order_by(TelnyxNumber.created_at.desc())
        result = await db.execute(query)
        numbers = result.scalars().all()

        return [TelnyxNumberResponse.model_validate(
            number) for number in numbers]

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error listing numbers for tenant {tenant_id}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/{number_id}",
    response_model=TelnyxNumberResponse,
    summary="Get phone number details",
    description="Get details for a specific phone number"
)
async def get_number_details(
    number_id: UUID,
    tenant_id: UUID = Depends(get_tenant_id),
    db: AsyncSession = Depends(get_db)
):
    """Get details for a specific phone number owned by the tenant."""
    try:
        query = select(TelnyxNumber).where(
            TelnyxNumber.id == number_id,
            TelnyxNumber.tenant_id == tenant_id
        )
        result = await db.execute(query)
        number = result.scalar_one_or_none()

        if not number:
            raise HTTPException(
                status_code=404,
                detail="Phone number not found"
            )

        return TelnyxNumberResponse.model_validate(number)

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(
            f"Error getting number {number_id} for tenant {tenant_id}")
        raise HTTPException(status_code=500, detail="Internal server error")
