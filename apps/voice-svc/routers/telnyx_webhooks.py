"""
Telnyx webhook handlers for number activation and other events.

Handles:
- Number activation webhooks
- Webhook signature verification
- Status updates in database
"""
import hashlib
import hmac
import json
import logging
import os
from typing import Dict, Any

from fastapi import APIRouter, Request, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from packages.shared.models import TelnyxNumber, TelnyxNumberStatus, TelnyxConnectionStatus
from db.session import get_db
from schemas.telnyx import TelnyxWebhookEvent, TelnyxNumberActivatedEvent

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/webhooks/telnyx", tags=["telnyx-webhooks"])


def verify_telnyx_signature(
        payload: bytes,
        signature: str,
        secret: str) -> bool:
    """
    Verify Telnyx webhook signature using HMAC-SHA256.

    Args:
        payload: Raw request body bytes
        signature: Signature from X-Telnyx-Signature header
        secret: Webhook secret from environment

    Returns:
        True if signature is valid, False otherwise
    """
    if not secret:
        logger.warning(
            "No webhook secret configured - skipping signature verification")
        return True  # Allow in development/testing

    try:
        # Telnyx sends signature in format: t=timestamp,v1=signature
        # We only need the v1 signature part
        sig_parts = {}
        for part in signature.split(','):
            key, value = part.split('=', 1)
            sig_parts[key] = value

        if 'v1' not in sig_parts:
            logger.error("No v1 signature found in webhook")
            return False

        expected_signature = sig_parts['v1']

        # Create HMAC signature
        computed_signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()

        # Compare signatures
        return hmac.compare_digest(expected_signature, computed_signature)

    except Exception as e:
        logger.error(f"Error verifying webhook signature: {e}")
        return False


async def get_webhook_secret() -> str:
    """Get webhook secret from environment."""
    return os.getenv("TELNYX_WEBHOOK_SECRET", "")


@router.post(
    "/number-activated",
    summary="Handle number activation webhook",
    description="Process Telnyx webhook when a phone number is activated"
)
async def handle_number_activated(
    request: Request,
    db: AsyncSession = Depends(get_db),
    webhook_secret: str = Depends(get_webhook_secret)
):
    """
    Handle Telnyx number activation webhook.

    Updates the number status to 'active' when Telnyx confirms activation.
    """
    try:
        # Get raw body and signature
        body = await request.body()
        signature = request.headers.get("X-Telnyx-Signature", "")

        # Verify signature
        if not verify_telnyx_signature(body, signature, webhook_secret):
            logger.warning("Invalid webhook signature")
            raise HTTPException(status_code=400, detail="Invalid signature")

        # Parse webhook payload
        try:
            payload = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in webhook payload: {e}")
            raise HTTPException(status_code=400, detail="Invalid JSON payload")

        # Validate webhook event structure
        try:
            webhook_event = TelnyxWebhookEvent(**payload)
        except Exception as e:
            logger.error(f"Invalid webhook event structure: {e}")
            raise HTTPException(status_code=400,
                                detail="Invalid webhook event structure")

        # Handle different event types
        event_type = webhook_event.event_type
        event_data = webhook_event.data

        if event_type == "phone_number.created":
            await handle_phone_number_created(event_data, db)
        elif event_type == "phone_number.updated":
            await handle_phone_number_updated(event_data, db)
        elif event_type == "phone_number.deleted":
            await handle_phone_number_deleted(event_data, db)
        else:
            logger.info(f"Unhandled webhook event type: {event_type}")

        return {"status": "ok", "event_type": event_type}

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error processing Telnyx webhook")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/number-configuration",
    summary="Handle number configuration updates",
    description="Process Telnyx webhook events for number configuration changes"
)
async def handle_number_configuration(
    request: Request,
    db: AsyncSession = Depends(get_db),
    webhook_secret: str = Depends(get_webhook_secret)
):
    """
    Handle Telnyx webhook events for number configuration updates.

    This endpoint processes events when:
    - Connection status changes (forwarding, SIP, etc.)
    - Number configuration is updated
    - Verification status changes
    """
    try:
        # Get raw body and signature
        body = await request.body()
        signature = request.headers.get("X-Telnyx-Signature", "")

        # Verify signature
        if not verify_telnyx_signature(body, signature, webhook_secret):
            logger.warning("Invalid webhook signature")
            raise HTTPException(status_code=400, detail="Invalid signature")

        # Parse webhook payload
        try:
            webhook_data = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in webhook payload: {e}")
            raise HTTPException(status_code=400, detail="Invalid JSON payload")

        # Parse the webhook event
        try:
            event = TelnyxWebhookEvent(**webhook_data)
        except Exception as e:
            logger.error(f"Invalid webhook event structure: {e}")
            raise HTTPException(status_code=400, detail="Invalid webhook event structure")

        logger.info(f"Received number-configuration webhook: {event.event_type}")

        # Handle different event types
        if event.event_type == "phone_number.updated":
            await _handle_number_configuration_update(event, db)
        elif event.event_type == "phone_number.connection_changed":
            await _handle_connection_change(event, db)
        else:
            logger.warning(f"Unhandled event type: {event.event_type}")
            return {"status": "ignored", "reason": f"Unhandled event type: {event.event_type}"}

        return {"status": "processed", "event_type": event.event_type}

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error processing number-configuration webhook")
        raise HTTPException(status_code=500, detail="Internal server error")


async def _handle_number_configuration_update(
    event: TelnyxWebhookEvent,
    db: AsyncSession
):
    """Handle phone number configuration updates."""
    try:
        payload = event.data
        phone_number = payload.get("phone_number")

        if not phone_number:
            logger.warning("No phone number in webhook payload")
            return

        # Find the number in our database
        query = select(TelnyxNumber).where(TelnyxNumber.did == phone_number)
        result = await db.execute(query)
        number = result.scalar_one_or_none()

        if not number:
            logger.warning(f"Phone number {phone_number} not found in database")
            return

        # Update connection status based on payload
        connection_id = payload.get("connection_id")
        call_forwarding = payload.get("call_forwarding")

        # Determine connection status
        if call_forwarding and call_forwarding.get("phone_number"):
            # Number is configured for forwarding
            number.connection_status = TelnyxConnectionStatus.FORWARDING
            number.forwarding_number = call_forwarding.get("phone_number")
            number.verification_required = True
        elif connection_id:
            # Number is connected to an application (SIP)
            number.connection_status = TelnyxConnectionStatus.SIP
            number.forwarding_number = None
            number.verification_required = False
        else:
            # Number is provisioned but not configured
            number.connection_status = TelnyxConnectionStatus.PROVISIONED
            number.verification_required = False

        # Update Telnyx connection ID
        number.telnyx_connection_id = connection_id

        # Update metadata
        if number.metadata_ is None:
            number.metadata_ = {}
        number.metadata_["last_webhook_update"] = event.occurred_at.isoformat()
        number.metadata_["webhook_payload"] = payload

        await db.commit()

        logger.info(
            f"Updated number {phone_number} configuration: "
            f"status={number.connection_status.value}, "
            f"verification_required={number.verification_required}"
        )

        # Send verification email if required
        if number.verification_required and number.connection_status == TelnyxConnectionStatus.FORWARDING:
            await _send_verification_email(number)

    except Exception as e:
        await db.rollback()
        logger.exception(f"Error handling number configuration update: {e}")
        raise


async def _handle_connection_change(
    event: TelnyxWebhookEvent,
    db: AsyncSession
):
    """Handle connection status changes."""
    try:
        payload = event.data
        phone_number = payload.get("phone_number")
        connection_status = payload.get("connection_status", "").lower()

        if not phone_number:
            logger.warning("No phone number in connection change webhook")
            return

        # Find the number in our database
        query = select(TelnyxNumber).where(TelnyxNumber.did == phone_number)
        result = await db.execute(query)
        number = result.scalar_one_or_none()

        if not number:
            logger.warning(f"Phone number {phone_number} not found in database")
            return

        # Map Telnyx connection status to our enum
        if connection_status == "active":
            if number.forwarding_number:
                number.connection_status = TelnyxConnectionStatus.FORWARDING
            else:
                number.connection_status = TelnyxConnectionStatus.SIP
        elif connection_status == "provisioned":
            number.connection_status = TelnyxConnectionStatus.PROVISIONED

        # Update metadata
        if number.metadata_ is None:
            number.metadata_ = {}
        number.metadata_["connection_change_event"] = {
            "occurred_at": event.occurred_at.isoformat(),
            "status": connection_status,
            "payload": payload
        }

        await db.commit()

        logger.info(
            f"Updated connection status for {phone_number}: {number.connection_status.value}"
        )

    except Exception as e:
        await db.rollback()
        logger.exception(f"Error handling connection change: {e}")
        raise


async def _send_verification_email(number: TelnyxNumber):
    """
    Send verification email to tenant when forwarding is configured.

    This would integrate with the notification service to send an email
    with verification steps for the forwarding configuration.
    """
    try:
        # TODO: Integrate with notification service
        # For now, just log the requirement
        logger.info(
            f"Verification email required for {number.did} "
            f"forwarding to {number.forwarding_number} "
            f"for tenant {number.tenant_id}"
        )

        # In a real implementation, this would:
        # 1. Get tenant contact information
        # 2. Generate verification instructions
        # 3. Send email via notification service
        # 4. Update verification_required status based on response

    except Exception as e:
        logger.exception(f"Error sending verification email for {number.did}: {e}")


async def handle_phone_number_created(
        event_data: Dict[str, Any], db: AsyncSession):
    """Handle phone_number.created webhook event."""
    try:
        # Parse event data
        number_data = TelnyxNumberActivatedEvent(**event_data)

        # Find the number in our database
        query = select(TelnyxNumber).where(
            TelnyxNumber.did == number_data.phone_number)
        result = await db.execute(query)
        telnyx_number = result.scalar_one_or_none()

        if not telnyx_number:
            logger.warning(
                f"Received activation webhook for unknown number: {number_data.phone_number}")
            return

        # Update status to active
        update_query = update(TelnyxNumber).where(
            TelnyxNumber.id == telnyx_number.id
        ).values(
            status=TelnyxNumberStatus.ACTIVE,
            telnyx_number_id=number_data.id,
            metadata_={
                **telnyx_number.metadata_,
                "activated_at": number_data.created_at.isoformat(),
                "activation_event": event_data
            }
        )

        await db.execute(update_query)
        await db.commit()

        logger.info(
            f"Number {number_data.phone_number} activated successfully")

    except Exception as e:
        await db.rollback()
        logger.error(f"Error handling phone_number.created event: {e}")
        raise


async def handle_phone_number_updated(
        event_data: Dict[str, Any], db: AsyncSession):
    """Handle phone_number.updated webhook event."""
    try:
        number_data = TelnyxNumberActivatedEvent(**event_data)

        # Find the number in our database
        query = select(TelnyxNumber).where(
            TelnyxNumber.did == number_data.phone_number)
        result = await db.execute(query)
        telnyx_number = result.scalar_one_or_none()

        if not telnyx_number:
            logger.warning(
                f"Received update webhook for unknown number: {number_data.phone_number}")
            return

        # Determine status based on Telnyx status
        new_status = TelnyxNumberStatus.ACTIVE
        if number_data.status in ["pending", "provisioning"]:
            new_status = TelnyxNumberStatus.PENDING
        elif number_data.status in ["inactive", "suspended"]:
            new_status = TelnyxNumberStatus.INACTIVE
        elif number_data.status in ["failed", "error"]:
            new_status = TelnyxNumberStatus.FAILED

        # Update the number
        update_query = update(TelnyxNumber).where(
            TelnyxNumber.id == telnyx_number.id
        ).values(
            status=new_status,
            telnyx_number_id=number_data.id,
            metadata_={
                **telnyx_number.metadata_,
                "last_updated": number_data.updated_at.isoformat(),
                "update_event": event_data
            }
        )

        await db.execute(update_query)
        await db.commit()

        logger.info(
            f"Number {number_data.phone_number} updated to status {new_status.value}")

    except Exception as e:
        await db.rollback()
        logger.error(f"Error handling phone_number.updated event: {e}")
        raise


async def handle_phone_number_deleted(
        event_data: Dict[str, Any], db: AsyncSession):
    """Handle phone_number.deleted webhook event."""
    try:
        number_data = TelnyxNumberActivatedEvent(**event_data)

        # Find the number in our database
        query = select(TelnyxNumber).where(
            TelnyxNumber.did == number_data.phone_number)
        result = await db.execute(query)
        telnyx_number = result.scalar_one_or_none()

        if not telnyx_number:
            logger.warning(
                f"Received deletion webhook for unknown number: {number_data.phone_number}")
            return

        # Update status to inactive (we don't delete the record for audit
        # purposes)
        update_query = update(TelnyxNumber).where(
            TelnyxNumber.id == telnyx_number.id
        ).values(
            status=TelnyxNumberStatus.INACTIVE,
            metadata_={
                **telnyx_number.metadata_,
                "deleted_at": number_data.updated_at.isoformat(),
                "deletion_event": event_data
            }
        )

        await db.execute(update_query)
        await db.commit()

        logger.info(f"Number {number_data.phone_number} marked as deleted")

    except Exception as e:
        await db.rollback()
        logger.error(f"Error handling phone_number.deleted event: {e}")
        raise
