"""
Telnyx webhook handlers for number activation and other events.

Handles:
- Number activation webhooks
- Webhook signature verification
- Status updates in database
"""
import hashlib
import hmac
import json
import logging
import os
from typing import Dict, Any

from fastapi import APIRouter, Request, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from packages.shared.models import TelnyxNumber, TelnyxNumberStatus
from db.session import get_db
from schemas.telnyx import TelnyxWebhookEvent, TelnyxNumberActivatedEvent

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/webhooks/telnyx", tags=["telnyx-webhooks"])


def verify_telnyx_signature(payload: bytes, signature: str, secret: str) -> bool:
    """
    Verify Telnyx webhook signature using HMAC-SHA256.
    
    Args:
        payload: Raw request body bytes
        signature: Signature from X-Telnyx-Signature header
        secret: Webhook secret from environment
        
    Returns:
        True if signature is valid, False otherwise
    """
    if not secret:
        logger.warning("No webhook secret configured - skipping signature verification")
        return True  # Allow in development/testing
    
    try:
        # Telnyx sends signature in format: t=timestamp,v1=signature
        # We only need the v1 signature part
        sig_parts = {}
        for part in signature.split(','):
            key, value = part.split('=', 1)
            sig_parts[key] = value
        
        if 'v1' not in sig_parts:
            logger.error("No v1 signature found in webhook")
            return False
        
        expected_signature = sig_parts['v1']
        
        # Create HMAC signature
        computed_signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        # Compare signatures
        return hmac.compare_digest(expected_signature, computed_signature)
        
    except Exception as e:
        logger.error(f"Error verifying webhook signature: {e}")
        return False


async def get_webhook_secret() -> str:
    """Get webhook secret from environment."""
    return os.getenv("TELNYX_WEBHOOK_SECRET", "")


@router.post(
    "/number-activated",
    summary="Handle number activation webhook",
    description="Process Telnyx webhook when a phone number is activated"
)
async def handle_number_activated(
    request: Request,
    db: AsyncSession = Depends(get_db),
    webhook_secret: str = Depends(get_webhook_secret)
):
    """
    Handle Telnyx number activation webhook.
    
    Updates the number status to 'active' when Telnyx confirms activation.
    """
    try:
        # Get raw body and signature
        body = await request.body()
        signature = request.headers.get("X-Telnyx-Signature", "")
        
        # Verify signature
        if not verify_telnyx_signature(body, signature, webhook_secret):
            logger.warning("Invalid webhook signature")
            raise HTTPException(status_code=400, detail="Invalid signature")
        
        # Parse webhook payload
        try:
            payload = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in webhook payload: {e}")
            raise HTTPException(status_code=400, detail="Invalid JSON payload")
        
        # Validate webhook event structure
        try:
            webhook_event = TelnyxWebhookEvent(**payload)
        except Exception as e:
            logger.error(f"Invalid webhook event structure: {e}")
            raise HTTPException(status_code=400, detail="Invalid webhook event structure")
        
        # Handle different event types
        event_type = webhook_event.event_type
        event_data = webhook_event.data
        
        if event_type == "phone_number.created":
            await handle_phone_number_created(event_data, db)
        elif event_type == "phone_number.updated":
            await handle_phone_number_updated(event_data, db)
        elif event_type == "phone_number.deleted":
            await handle_phone_number_deleted(event_data, db)
        else:
            logger.info(f"Unhandled webhook event type: {event_type}")
        
        return {"status": "ok", "event_type": event_type}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error processing Telnyx webhook")
        raise HTTPException(status_code=500, detail="Internal server error")


async def handle_phone_number_created(event_data: Dict[str, Any], db: AsyncSession):
    """Handle phone_number.created webhook event."""
    try:
        # Parse event data
        number_data = TelnyxNumberActivatedEvent(**event_data)
        
        # Find the number in our database
        query = select(TelnyxNumber).where(TelnyxNumber.did == number_data.phone_number)
        result = await db.execute(query)
        telnyx_number = result.scalar_one_or_none()
        
        if not telnyx_number:
            logger.warning(f"Received activation webhook for unknown number: {number_data.phone_number}")
            return
        
        # Update status to active
        update_query = update(TelnyxNumber).where(
            TelnyxNumber.id == telnyx_number.id
        ).values(
            status=TelnyxNumberStatus.ACTIVE,
            telnyx_number_id=number_data.id,
            metadata_={
                **telnyx_number.metadata_,
                "activated_at": number_data.created_at.isoformat(),
                "activation_event": event_data
            }
        )
        
        await db.execute(update_query)
        await db.commit()
        
        logger.info(f"Number {number_data.phone_number} activated successfully")
        
    except Exception as e:
        await db.rollback()
        logger.error(f"Error handling phone_number.created event: {e}")
        raise


async def handle_phone_number_updated(event_data: Dict[str, Any], db: AsyncSession):
    """Handle phone_number.updated webhook event."""
    try:
        number_data = TelnyxNumberActivatedEvent(**event_data)
        
        # Find the number in our database
        query = select(TelnyxNumber).where(TelnyxNumber.did == number_data.phone_number)
        result = await db.execute(query)
        telnyx_number = result.scalar_one_or_none()
        
        if not telnyx_number:
            logger.warning(f"Received update webhook for unknown number: {number_data.phone_number}")
            return
        
        # Determine status based on Telnyx status
        new_status = TelnyxNumberStatus.ACTIVE
        if number_data.status in ["pending", "provisioning"]:
            new_status = TelnyxNumberStatus.PENDING
        elif number_data.status in ["inactive", "suspended"]:
            new_status = TelnyxNumberStatus.INACTIVE
        elif number_data.status in ["failed", "error"]:
            new_status = TelnyxNumberStatus.FAILED
        
        # Update the number
        update_query = update(TelnyxNumber).where(
            TelnyxNumber.id == telnyx_number.id
        ).values(
            status=new_status,
            telnyx_number_id=number_data.id,
            metadata_={
                **telnyx_number.metadata_,
                "last_updated": number_data.updated_at.isoformat(),
                "update_event": event_data
            }
        )
        
        await db.execute(update_query)
        await db.commit()
        
        logger.info(f"Number {number_data.phone_number} updated to status {new_status.value}")
        
    except Exception as e:
        await db.rollback()
        logger.error(f"Error handling phone_number.updated event: {e}")
        raise


async def handle_phone_number_deleted(event_data: Dict[str, Any], db: AsyncSession):
    """Handle phone_number.deleted webhook event."""
    try:
        number_data = TelnyxNumberActivatedEvent(**event_data)
        
        # Find the number in our database
        query = select(TelnyxNumber).where(TelnyxNumber.did == number_data.phone_number)
        result = await db.execute(query)
        telnyx_number = result.scalar_one_or_none()
        
        if not telnyx_number:
            logger.warning(f"Received deletion webhook for unknown number: {number_data.phone_number}")
            return
        
        # Update status to inactive (we don't delete the record for audit purposes)
        update_query = update(TelnyxNumber).where(
            TelnyxNumber.id == telnyx_number.id
        ).values(
            status=TelnyxNumberStatus.INACTIVE,
            metadata_={
                **telnyx_number.metadata_,
                "deleted_at": number_data.updated_at.isoformat(),
                "deletion_event": event_data
            }
        )
        
        await db.execute(update_query)
        await db.commit()
        
        logger.info(f"Number {number_data.phone_number} marked as deleted")
        
    except Exception as e:
        await db.rollback()
        logger.error(f"Error handling phone_number.deleted event: {e}")
        raise
