"""
Telnyx service for DID search and purchase operations.

This service handles:
- Searching for available phone numbers by state
- Purchasing/provisioning phone numbers
- Managing number status and metadata
"""
import logging
import os
from typing import List
from uuid import UUID
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from packages.shared.models import TelnyxNumber, TelnyxNumberStatus
from schemas.telnyx import TelnyxAvailableNumber

logger = logging.getLogger(__name__)


class TelnyxAPIError(Exception):
    """Custom exception for Telnyx API errors."""
    def __init__(self, message: str, status_code: int = None, error_code: str = None):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        super().__init__(self.message)


class TelnyxService:
    """Service for interacting with Telnyx API and managing DID numbers."""
    
    def __init__(self):
        self.api_key = os.getenv("TELNYX_API_KEY")
        if not self.api_key:
            raise ValueError("TELNYX_API_KEY environment variable is required")
        
        self.base_url = "https://api.telnyx.com/v2"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
    
    @retry(
        stop=stop_after_attempt(2),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError)),
        reraise=True
    )
    async def search_numbers(self, state: str, limit: int = 10) -> List[TelnyxAvailableNumber]:
        """
        Search for available phone numbers in a specific US state.
        
        Args:
            state: Two-letter US state code (e.g., 'TX', 'CA')
            limit: Maximum number of results to return (default: 10, max: 250)
            
        Returns:
            List of available phone numbers with metadata
            
        Raises:
            TelnyxAPIError: If the API request fails
        """
        if not state or len(state) != 2:
            raise ValueError("State must be a 2-letter US state code")
        
        if limit < 1 or limit > 250:
            raise ValueError("Limit must be between 1 and 250")
        
        params = {
            "filter[country_code]": "US",
            "filter[administrative_area]": state.upper(),
            "filter[number_type]": "local",
            "filter[features][]": ["voice", "sms"],
            "page[size]": min(limit, 250),
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.base_url}/available_phone_numbers",
                    headers=self.headers,
                    params=params
                )
                
                if response.status_code == 404:
                    logger.warning(f"No numbers available for state: {state}")
                    return []
                
                response.raise_for_status()
                data = response.json()
                
                numbers = []
                for item in data.get("data", []):
                    numbers.append(TelnyxAvailableNumber(
                        phone_number=item["phone_number"],
                        region_information=[{
                            "region_type": info.get("region_type"),
                            "region_name": info.get("region_name")
                        } for info in item.get("region_information", [])],
                        features=item.get("features", []),
                        cost_information=item.get("cost_information", {}),
                        best_effort=item.get("best_effort", False),
                        quickship=item.get("quickship", False),
                        reservable=item.get("reservable", False)
                    ))
                
                logger.info(f"Found {len(numbers)} available numbers for state {state}")
                return numbers
                
        except httpx.HTTPStatusError as e:
            error_msg = f"Telnyx API error: {e.response.status_code}"
            try:
                error_data = e.response.json()
                error_msg += f" - {error_data.get('errors', [{}])[0].get('detail', 'Unknown error')}"
            except:
                error_msg += f" - {e.response.text}"
            
            logger.error(error_msg)
            raise TelnyxAPIError(error_msg, e.response.status_code)
            
        except httpx.RequestError as e:
            error_msg = f"Network error communicating with Telnyx: {str(e)}"
            logger.error(error_msg)
            raise TelnyxAPIError(error_msg)
    
    @retry(
        stop=stop_after_attempt(2),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((httpx.HTTPStatusError, httpx.RequestError)),
        reraise=True
    )
    async def purchase_number(self, tenant_id: UUID, did: str) -> TelnyxNumber:
        """
        Purchase a phone number from Telnyx.
        
        Args:
            tenant_id: UUID of the tenant purchasing the number
            did: Phone number to purchase (E.164 format, e.g., '+18325550123')
            
        Returns:
            TelnyxNumber model instance with purchase details
            
        Raises:
            TelnyxAPIError: If the purchase fails
        """
        if not did.startswith('+'):
            raise ValueError("DID must be in E.164 format (starting with +)")
        
        # Extract state from DID (simplified - in production you'd use a proper lookup)
        # For now, we'll extract from area code mapping
        state = self._extract_state_from_did(did)
        
        payload = {
            "phone_number": did,
            "connection_id": None,  # Will be set when configuring forwarding
            "customer_reference": str(tenant_id),
            "billing_group_id": None,  # Optional: for billing organization
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/phone_numbers",
                    headers=self.headers,
                    json=payload
                )
                
                if response.status_code == 409:
                    # Number already exists/purchased
                    error_msg = f"Phone number {did} is already purchased or unavailable"
                    logger.warning(error_msg)
                    raise TelnyxAPIError(error_msg, 409, "number_unavailable")
                
                response.raise_for_status()
                data = response.json()
                
                # Extract Telnyx response data
                telnyx_data = data.get("data", {})
                telnyx_number_id = telnyx_data.get("id")
                
                # Create TelnyxNumber model instance
                telnyx_number = TelnyxNumber(
                    tenant_id=tenant_id,
                    did=did,
                    state=state,
                    forwarding_number=None,  # Will be set in T-2
                    status=TelnyxNumberStatus.PENDING,
                    telnyx_number_id=telnyx_number_id,
                    metadata_={
                        "purchase_response": telnyx_data,
                        "purchased_at": telnyx_data.get("created_at"),
                        "phone_number_type": telnyx_data.get("phone_number_type"),
                        "regulatory_requirements": telnyx_data.get("regulatory_requirements", []),
                    }
                )
                
                logger.info(f"Successfully purchased number {did} for tenant {tenant_id}")
                return telnyx_number
                
        except httpx.HTTPStatusError as e:
            error_msg = f"Failed to purchase number {did}: {e.response.status_code}"
            try:
                error_data = e.response.json()
                errors = error_data.get('errors', [{}])
                if errors:
                    error_detail = errors[0].get('detail', 'Unknown error')
                    error_code = errors[0].get('code', 'unknown')
                    error_msg += f" - {error_detail}"
                    
                    # Map specific Telnyx errors to our error codes
                    if "already exists" in error_detail.lower() or error_code == "10015":
                        raise TelnyxAPIError(error_msg, 409, "number_unavailable")
                        
            except:
                error_msg += f" - {e.response.text}"
            
            logger.error(error_msg)
            raise TelnyxAPIError(error_msg, e.response.status_code)
            
        except httpx.RequestError as e:
            error_msg = f"Network error purchasing number {did}: {str(e)}"
            logger.error(error_msg)
            raise TelnyxAPIError(error_msg)
    
    def _extract_state_from_did(self, did: str) -> str:
        """
        Extract state code from DID based on area code.
        This is a simplified implementation - in production you'd use a comprehensive lookup.
        """
        # Remove + and country code for US numbers
        if did.startswith('+1'):
            area_code = did[2:5]
        else:
            area_code = did[1:4] if did.startswith('+') else did[:3]
        
        # Simplified area code to state mapping (just a few examples)
        area_code_map = {
            '832': 'TX', '713': 'TX', '281': 'TX', '409': 'TX', '430': 'TX',
            '214': 'TX', '469': 'TX', '972': 'TX', '945': 'TX',
            '415': 'CA', '510': 'CA', '650': 'CA', '925': 'CA', '408': 'CA',
            '212': 'NY', '646': 'NY', '917': 'NY', '718': 'NY', '347': 'NY',
            '305': 'FL', '786': 'FL', '954': 'FL', '561': 'FL', '772': 'FL',
        }
        
        return area_code_map.get(area_code, 'TX')  # Default to TX if not found
