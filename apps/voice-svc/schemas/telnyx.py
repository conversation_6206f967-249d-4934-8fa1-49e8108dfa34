"""
Pydantic schemas for Telnyx DID operations.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field, validator


class RegionInformation(BaseModel):
    """Region information for a phone number."""
    region_type: Optional[str] = None
    region_name: Optional[str] = None


class CostInformation(BaseModel):
    """Cost information for a phone number."""
    upfront_cost: Optional[str] = None
    monthly_cost: Optional[str] = None
    currency: Optional[str] = "USD"


class TelnyxAvailableNumber(BaseModel):
    """Schema for available phone numbers from Telnyx search."""
    phone_number: str = Field(..., description="Phone number in E.164 format")
    region_information: List[RegionInformation] = Field(default_factory=list)
    features: List[str] = Field(
        default_factory=list,
        description="Available features (voice, sms, etc.)")
    cost_information: Dict[str, Any] = Field(default_factory=dict)
    best_effort: bool = Field(
        default=False,
        description="Whether this is a best effort number")
    quickship: bool = Field(
        default=False,
        description="Whether this number can be quickly provisioned")
    reservable: bool = Field(default=False,
                             description="Whether this number can be reserved")


class TelnyxNumberCreate(BaseModel):
    """Schema for purchasing a phone number."""
    state: str = Field(..., min_length=2, max_length=2,
                       description="Two-letter US state code")
    did: str = Field(...,
                     description="Phone number to purchase in E.164 format")

    @validator('state')
    def validate_state(cls, v):
        if not v.isupper():
            v = v.upper()
        # Basic validation - in production you'd validate against a list of
        # valid state codes
        if len(v) != 2 or not v.isalpha():
            raise ValueError('State must be a 2-letter code')
        return v

    @validator('did')
    def validate_did(cls, v):
        if not v.startswith('+'):
            raise ValueError('DID must be in E.164 format (starting with +)')
        if not v[1:].isdigit():
            raise ValueError('DID must contain only digits after the + sign')
        if len(v) < 10 or len(v) > 15:
            raise ValueError('DID must be between 10 and 15 characters')
        return v


class TelnyxNumberResponse(BaseModel):
    """Schema for Telnyx number response."""
    id: UUID
    tenant_id: UUID
    did: str
    state: str
    forwarding_number: Optional[str] = None
    status: str
    telnyx_number_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None,
            UUID: lambda v: str(v) if v else None
        }


class TelnyxNumberUpdate(BaseModel):
    """Schema for updating a Telnyx number."""
    forwarding_number: Optional[str] = None
    status: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    @validator('forwarding_number')
    def validate_forwarding_number(cls, v):
        if v is not None:
            if not v.startswith('+'):
                raise ValueError(
                    'Forwarding number must be in E.164 format (starting with +)')
            if not v[1:].isdigit():
                raise ValueError(
                    'Forwarding number must contain only digits after the + sign')
        return v


class TelnyxWebhookEvent(BaseModel):
    """Schema for Telnyx webhook events."""
    data: Dict[str, Any]
    event_type: str
    id: str
    occurred_at: datetime
    record_type: str = "event"

    @validator('occurred_at', pre=True)
    def parse_occurred_at(cls, v):
        if isinstance(v, str):
            # Parse ISO format datetime
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v


class TelnyxNumberActivatedEvent(BaseModel):
    """Schema for number activated webhook event."""
    id: str
    phone_number: str
    status: str
    connection_id: Optional[str] = None
    customer_reference: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    @validator('created_at', 'updated_at', pre=True)
    def parse_datetime(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v.replace('Z', '+00:00'))
        return v


class NumberSearchRequest(BaseModel):
    """Schema for number search request."""
    state: str = Field(..., min_length=2, max_length=2,
                       description="Two-letter US state code")
    limit: int = Field(
        default=10,
        ge=1,
        le=250,
        description="Number of results to return")

    @validator('state')
    def validate_state(cls, v):
        return v.upper()


class NumberSearchResponse(BaseModel):
    """Schema for number search response."""
    numbers: List[TelnyxAvailableNumber]
    total_count: int
    state: str


class ErrorResponse(BaseModel):
    """Schema for error responses."""
    detail: str
    error_code: Optional[str] = None
    status_code: int = 400


class SuccessResponse(BaseModel):
    """Schema for success responses."""
    message: str
    data: Optional[Dict[str, Any]] = None
