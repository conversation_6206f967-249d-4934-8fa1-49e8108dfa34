["tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_api_error", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_invalid_did", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_invalid_forward_to", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_number_not_found", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_premium_rate_blocked", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_self_forwarding", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_success", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_sip_app_api_error", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_sip_app_invalid_did", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_sip_app_no_app_id", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_sip_app_success"]