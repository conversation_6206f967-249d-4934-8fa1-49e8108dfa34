"""
Integration tests for Telnyx API endpoints.
"""
import json
import pytest
import respx
import httpx
from uuid import UUID

from packages.shared.models import TelnyxNumber, TelnyxNumberStatus


class TestTelnyxEndpoints:
    """Test cases for Telnyx API endpoints."""
    
    @respx.mock
    def test_search_available_numbers_success(self, test_client, tenant_id):
        """Test successful number search endpoint."""
        # Mock Telnyx API response
        mock_response = {
            "data": [
                {
                    "phone_number": "+18325550123",
                    "region_information": [
                        {"region_type": "state", "region_name": "Texas"}
                    ],
                    "features": ["voice", "sms"],
                    "cost_information": {"upfront_cost": "1.00", "monthly_cost": "1.00"},
                    "best_effort": False,
                    "quickship": True,
                    "reservable": True
                }
            ]
        }
        
        respx.get(
            "https://api.telnyx.com/v2/available_phone_numbers"
        ).mock(return_value=httpx.Response(200, json=mock_response))
        
        # Make request
        response = test_client.get(
            "/api/v1/numbers/available?state=TX&limit=10",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["state"] == "TX"
        assert data["total_count"] == 1
        assert len(data["numbers"]) == 1
        assert data["numbers"][0]["phone_number"] == "+18325550123"
    
    def test_search_available_numbers_missing_tenant_id(self, test_client):
        """Test number search without tenant ID header."""
        response = test_client.get("/api/v1/numbers/available?state=TX")
        assert response.status_code == 422  # Missing required header
    
    def test_search_available_numbers_invalid_state(self, test_client, tenant_id):
        """Test number search with invalid state parameter."""
        response = test_client.get(
            "/api/v1/numbers/available?state=TEXAS",
            headers={"X-Tenant-ID": tenant_id}
        )
        assert response.status_code == 422  # Validation error
    
    @respx.mock
    async def test_purchase_number_success(self, test_client, test_db, tenant_id, sample_telnyx_response):
        """Test successful number purchase endpoint."""
        respx.post(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(201, json=sample_telnyx_response))
        
        # Make purchase request
        response = test_client.post(
            "/api/v1/numbers/purchase",
            json={"state": "TX", "did": "+18325550123"},
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["did"] == "+18325550123"
        assert data["state"] == "TX"
        assert data["status"] == "pending"
        assert data["tenant_id"] == tenant_id
        
        # Verify database record was created
        from sqlalchemy import select
        query = select(TelnyxNumber).where(TelnyxNumber.did == "+18325550123")
        result = await test_db.execute(query)
        db_number = result.scalar_one_or_none()
        
        assert db_number is not None
        assert str(db_number.tenant_id) == tenant_id
        assert db_number.status == TelnyxNumberStatus.PENDING
    
    async def test_purchase_number_duplicate(self, test_client, test_db, tenant_id):
        """Test purchasing a number that already exists in database."""
        # Create existing number in database
        existing_number = TelnyxNumber(
            tenant_id=UUID(tenant_id),
            did="+18325550123",
            state="TX",
            status=TelnyxNumberStatus.ACTIVE
        )
        test_db.add(existing_number)
        await test_db.commit()
        
        # Try to purchase the same number
        response = test_client.post(
            "/api/v1/numbers/purchase",
            json={"state": "TX", "did": "+18325550123"},
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 409
        assert "already exists" in response.json()["detail"]
    
    def test_purchase_number_invalid_did(self, test_client, tenant_id):
        """Test purchasing with invalid DID format."""
        response = test_client.post(
            "/api/v1/numbers/purchase",
            json={"state": "TX", "did": "18325550123"},  # Missing +
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 422  # Validation error
    
    @respx.mock
    def test_purchase_number_telnyx_error(self, test_client, tenant_id):
        """Test purchase with Telnyx API error."""
        error_response = {
            "errors": [
                {
                    "code": "10015",
                    "detail": "Phone number already exists",
                    "title": "Conflict"
                }
            ]
        }
        
        respx.post(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(409, json=error_response))
        
        response = test_client.post(
            "/api/v1/numbers/purchase",
            json={"state": "TX", "did": "+18325550123"},
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 409
    
    async def test_list_tenant_numbers(self, test_client, test_db, tenant_id):
        """Test listing tenant's phone numbers."""
        # Create test numbers
        number1 = TelnyxNumber(
            tenant_id=UUID(tenant_id),
            did="+18325550123",
            state="TX",
            status=TelnyxNumberStatus.ACTIVE
        )
        number2 = TelnyxNumber(
            tenant_id=UUID(tenant_id),
            did="+18325550124",
            state="TX",
            status=TelnyxNumberStatus.PENDING
        )
        
        test_db.add_all([number1, number2])
        await test_db.commit()
        
        # List all numbers
        response = test_client.get(
            "/api/v1/numbers/",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        
        # List only active numbers
        response = test_client.get(
            "/api/v1/numbers/?status=active",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["status"] == "active"
    
    async def test_get_number_details(self, test_client, test_db, tenant_id):
        """Test getting details for a specific number."""
        # Create test number
        number = TelnyxNumber(
            tenant_id=UUID(tenant_id),
            did="+18325550123",
            state="TX",
            status=TelnyxNumberStatus.ACTIVE
        )
        
        test_db.add(number)
        await test_db.commit()
        await test_db.refresh(number)
        
        # Get number details
        response = test_client.get(
            f"/api/v1/numbers/{number.id}",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["did"] == "+18325550123"
        assert data["status"] == "active"
    
    def test_get_number_details_not_found(self, test_client, tenant_id):
        """Test getting details for non-existent number."""
        fake_id = "550e8400-e29b-41d4-a716-************"
        
        response = test_client.get(
            f"/api/v1/numbers/{fake_id}",
            headers={"X-Tenant-ID": tenant_id}
        )
        
        assert response.status_code == 404
