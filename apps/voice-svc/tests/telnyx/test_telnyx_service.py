"""
Unit tests for TelnyxService.
"""
import pytest
import respx
import httpx
from uuid import UUID

from services.telnyx_service import TelnyxService, TelnyxAPIError
from packages.shared.models import TelnyxNumberStatus


class TestTelnyxService:
    """Test cases for TelnyxService."""
    
    @pytest.fixture
    def telnyx_service(self):
        """Create TelnyxService instance for testing."""
        return TelnyxService()
    
    @respx.mock
    async def test_search_numbers_success(self, telnyx_service):
        """Test successful number search."""
        # Mock Telnyx API response
        mock_response = {
            "data": [
                {
                    "phone_number": "+18325550123",
                    "region_information": [
                        {"region_type": "state", "region_name": "Texas"}
                    ],
                    "features": ["voice", "sms"],
                    "cost_information": {"upfront_cost": "1.00", "monthly_cost": "1.00"},
                    "best_effort": False,
                    "quickship": True,
                    "reservable": True
                },
                {
                    "phone_number": "+18325550124",
                    "region_information": [
                        {"region_type": "state", "region_name": "Texas"}
                    ],
                    "features": ["voice", "sms"],
                    "cost_information": {"upfront_cost": "1.00", "monthly_cost": "1.00"},
                    "best_effort": False,
                    "quickship": True,
                    "reservable": True
                }
            ]
        }
        
        respx.get(
            "https://api.telnyx.com/v2/available_phone_numbers"
        ).mock(return_value=httpx.Response(200, json=mock_response))
        
        # Test the search
        numbers = await telnyx_service.search_numbers("TX", 10)
        
        assert len(numbers) == 2
        assert numbers[0].phone_number == "+18325550123"
        assert numbers[0].features == ["voice", "sms"]
        assert numbers[0].quickship is True
    
    @respx.mock
    async def test_search_numbers_no_results(self, telnyx_service):
        """Test number search with no results."""
        respx.get(
            "https://api.telnyx.com/v2/available_phone_numbers"
        ).mock(return_value=httpx.Response(404))
        
        numbers = await telnyx_service.search_numbers("ZZ", 10)
        assert len(numbers) == 0
    
    @respx.mock
    async def test_search_numbers_api_error(self, telnyx_service):
        """Test number search with API error."""
        respx.get(
            "https://api.telnyx.com/v2/available_phone_numbers"
        ).mock(return_value=httpx.Response(500, text="Internal Server Error"))
        
        with pytest.raises(TelnyxAPIError) as exc_info:
            await telnyx_service.search_numbers("TX", 10)
        
        assert "500" in str(exc_info.value)
    
    async def test_search_numbers_invalid_state(self, telnyx_service):
        """Test number search with invalid state."""
        with pytest.raises(ValueError, match="State must be a 2-letter US state code"):
            await telnyx_service.search_numbers("", 10)
        
        with pytest.raises(ValueError, match="State must be a 2-letter US state code"):
            await telnyx_service.search_numbers("TEX", 10)
    
    async def test_search_numbers_invalid_limit(self, telnyx_service):
        """Test number search with invalid limit."""
        with pytest.raises(ValueError, match="Limit must be between 1 and 250"):
            await telnyx_service.search_numbers("TX", 0)
        
        with pytest.raises(ValueError, match="Limit must be between 1 and 250"):
            await telnyx_service.search_numbers("TX", 300)
    
    @respx.mock
    async def test_purchase_number_success(self, telnyx_service, sample_telnyx_response):
        """Test successful number purchase."""
        respx.post(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(201, json=sample_telnyx_response))
        
        tenant_id = UUID("550e8400-e29b-41d4-a716-************")
        did = "+18325550123"
        
        telnyx_number = await telnyx_service.purchase_number(tenant_id, did)
        
        assert telnyx_number.tenant_id == tenant_id
        assert telnyx_number.did == did
        assert telnyx_number.state == "TX"
        assert telnyx_number.status == TelnyxNumberStatus.PENDING
        assert telnyx_number.telnyx_number_id == "telnyx_number_123"
    
    @respx.mock
    async def test_purchase_number_already_exists(self, telnyx_service):
        """Test purchasing a number that already exists."""
        error_response = {
            "errors": [
                {
                    "code": "10015",
                    "detail": "Phone number already exists",
                    "title": "Conflict"
                }
            ]
        }
        
        respx.post(
            "https://api.telnyx.com/v2/phone_numbers"
        ).mock(return_value=httpx.Response(409, json=error_response))
        
        tenant_id = UUID("550e8400-e29b-41d4-a716-************")
        did = "+18325550123"
        
        with pytest.raises(TelnyxAPIError) as exc_info:
            await telnyx_service.purchase_number(tenant_id, did)
        
        assert exc_info.value.status_code == 409
        assert exc_info.value.error_code == "number_unavailable"
    
    async def test_purchase_number_invalid_did(self, telnyx_service):
        """Test purchasing with invalid DID format."""
        tenant_id = UUID("550e8400-e29b-41d4-a716-************")
        
        with pytest.raises(ValueError, match="DID must be in E.164 format"):
            await telnyx_service.purchase_number(tenant_id, "18325550123")
    
    def test_extract_state_from_did(self, telnyx_service):
        """Test state extraction from DID."""
        # Test Texas numbers
        assert telnyx_service._extract_state_from_did("+18325550123") == "TX"
        assert telnyx_service._extract_state_from_did("+17135550123") == "TX"
        
        # Test California numbers
        assert telnyx_service._extract_state_from_did("+14155550123") == "CA"
        assert telnyx_service._extract_state_from_did("+15105550123") == "CA"
        
        # Test unknown area code (defaults to TX)
        assert telnyx_service._extract_state_from_did("+19995550123") == "TX"
