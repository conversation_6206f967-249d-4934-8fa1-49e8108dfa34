# Application
NODE_ENV=development

# Voice Agent (Pipecat)
PIPECAT_PROJECT_ID=
PIPECAT_API_KEY=
DEEPGRAM_API_KEY=
CARTESIA_API_KEY=

# Telnyx
TELNYX_API_KEY=
TELNYX_PUBLIC_KEY=
TELNYX_APP_ID=

# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Google Calendar
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI=

# Email (Resend)
RESEND_API_KEY=your_resend_api_key
EMAIL_FROM=<EMAIL>

# Webhook secrets for notification service
RESEND_WEBHOOK_SECRET=your_resend_webhook_secret
TELNYX_WEBHOOK_SECRET=your_telnyx_webhook_secret

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=your_google_redirect_uri

# Calendly OAuth
CALENDLY_CLIENT_ID=your_calendly_client_id
CALENDLY_CLIENT_SECRET=your_calendly_client_secret
CALENDLY_REDIRECT_URI=your_calendly_redirect_uri

# Core Subscription API (for AVR middleware)
CORE_SUBS_API_URL=https://core-api.example.com

# Redis (for AVR caching)
REDIS_URL=redis://localhost:6379/0

# JWT
JWT_SECRET_KEY=your_jwt_secret_key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database
DATABASE_URL=**************************************/calendar_db
DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@db:5432/calendar_db

# Redis
REDIS_URL=redis://redis:6379/0

# App
ENVIRONMENT=development
DEBUG=True

# CORS
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET= # Generate with: openssl rand -base64 32

# Calendly API
CALENDLY_API_BASE=https://api.calendly.com
CACHE_URL=redis://localhost:6379/1

# Auth Service
AUTH_SERVICE_BASE=https://ailex-auth.fly.dev
